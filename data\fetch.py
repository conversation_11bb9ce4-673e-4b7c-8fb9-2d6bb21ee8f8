import requests
from bs4 import BeautifulSoup
import pandas as pd
import datetime
import re
import chardet
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger()

def fetch_stock_data():
    try:
        # Check network connectivity
        test_response = requests.get("https://www.google.com", timeout=5)
        test_response.raise_for_status()

        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
        today = datetime.datetime.now().date()

        def parse_table(soup, table_class, is_public=True):
            table = soup.find('table', {'class': table_class})
            if not table:
                return None, f"無法找到{'申購' if is_public else '競拍'}表格"
            rows = table.find_all('tr')[1:]
            temp_data = []
            for row in rows:
                cols = row.find_all('td')
                if len(cols) < (10 if is_public else 15):
                    continue
                date = cols[0].text.strip()[:10]
                code_name = cols[1].text.strip()
                match = re.match(r'(\d+)(.+)', code_name)
                stock_code = match.group(1) if match else ""
                stock_name = match.group(2).strip() if match else code_name
                try:
                    date_obj = pd.to_datetime(date, format='%Y/%m/%d').date()
                    if date_obj < today:
                        continue
                except ValueError:
                    continue
                if is_public:
                    data = {
                        "日期": date,
                        "代號": stock_code,
                        "名稱": stock_name,
                        "申購日期": cols[3].text.strip(),
                        "日期對象": date_obj,
                        "承銷價": cols[6].text.strip(),
                        "獲利": cols[8].text.strip(),
                        "報酬率(%)": cols[9].text.strip()
                    }
                else:
                    data = {
                        "日期": date,
                        "代號": stock_code,
                        "名稱": stock_name,
                        "投標日期": cols[3].text.strip(),
                        "日期對象": date_obj,
                        "競標張數": cols[4].text.strip(),
                        "最低投標價格": cols[6].text.strip(),
                        "市價": cols[14].text.strip()
                    }
                temp_data.append(data)
            return sorted(temp_data, key=lambda x: x["日期對象"], reverse=True), None

        # Fetch public subscription data
        response_public = requests.get("https://histock.tw/stock/public.aspx", headers=headers)
        soup_public = BeautifulSoup(response_public.text, 'html.parser')
        public_data, public_error = parse_table(soup_public, 'gvTB', is_public=True)
        if public_error:
            return [], [], public_error

        # Fetch bid data
        response_bid = requests.get("https://histock.tw/stock/bid.aspx", headers=headers)
        soup_bid = BeautifulSoup(response_bid.text, 'html.parser')
        bid_data, bid_error = parse_table(soup_bid, 'gvTB', is_public=False)
        if bid_error:
            return [], [], bid_error

        return public_data, bid_data, None

    except Exception as e:
        error_msg = f"抓取資料時發生錯誤: {str(e)}\n請檢查:\n1. 網路連線\n2. 防毒軟體設定\n3. 系統時間是否正確"
        return [], [], error_msg

def fetch_infomation_stocks(url):
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
    except requests.RequestException as e:
        logger.error(f"❌ 無法獲取重大資訊網頁內容: {e}")
        return None

    soup = BeautifulSoup(response.text, 'html.parser')
    table = soup.find('table', class_='hasBorder')
    rows = table.find_all('tr')
    datalist = []

    for row in rows:
        if row.get('class') and 'tblHead' in row.get('class'):
            continue
        cells = row.find_all('td')
        if len(cells) < 5:
            continue
        datalist.append([
            cells[0].get_text(strip=True),
            cells[1].get_text(strip=True),
            cells[2].get_text(strip=True),
            cells[3].get_text(strip=True),
            cells[4].get_text(strip=True)
        ])

    return pd.DataFrame(datalist, columns=['股票代號', '股票名稱', '日期', '時間', '主旨']) if datalist else None

def fetch_attention_stocks(url):
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
    except requests.RequestException as e:
        logger.error(f"❌ 無法獲取注意股網頁內容: {e}")
        return None

    soup = BeautifulSoup(response.text, 'html.parser')
    rows = soup.find_all('tr')
    data = [[rows[1].find('td', class_='t10').find('div', class_='t11').text.strip()]]
    for row in rows[3:]:
        stock_td = row.find('td', class_='t3t1')
        info_td = row.find('td', class_='t3n1')
        if stock_td and info_td:
            script_tag = stock_td.find('script', language='javascript')
            stock_name = stock_td.get_text(strip=True) if not script_tag else ""
            if script_tag:
                match = re.search(r"GenLink2stk\('AS(\d+)','([^']+)'\);", script_tag.get_text(strip=True))
                if match:
                    stock_name = f"{match.group(1)} {match.group(2)}"
            data.append([stock_name, info_td.get_text(strip=True)])
    return pd.DataFrame(data, columns=['股票名稱', '事項']) if data else None

def fetch_listed_weekly_performance(url):
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        text = response.content.decode(chardet.detect(response.content)['encoding'] or 'utf-8', errors='ignore')
    except requests.RequestException as e:
        logger.error(f"❌ 無法獲取上市週漲幅網頁內容: {e}")
        return None

    soup = BeautifulSoup(text, 'html.parser')
    datalist, buffer = [], []
    flags = False
    for td in soup.find_all('td'):
        text = td.text.strip()
        try:
            int(text)
            flags = True
        except ValueError:
            pass
        if flags:
            buffer.append(text)
            if len(buffer) % 8 == 0:
                datalist.append(buffer)
                buffer = []
    return pd.DataFrame(datalist, columns=['名次', '股票名稱', '收盤價', '漲跌', '漲跌幅', '成交量', '1週漲跌', '1週漲跌幅']) if datalist else None

def fetch_otc_weekly_performance(url):
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        text = response.content.decode(chardet.detect(response.content)['encoding'] or 'utf-8', errors='ignore')
    except requests.RequestException as e:
        logger.error(f"❌ 無法獲取上櫃週漲幅網頁內容: {e}")
        return None

    soup = BeautifulSoup(text, 'html.parser')
    datalist, buffer = [], []
    flags = False
    for td in soup.find_all('td'):
        text = td.text.strip()
        try:
            int(text)
            flags = True
        except ValueError:
            pass
        if flags:
            buffer.append(text)
            if len(buffer) % 8 == 0:
                datalist.append(buffer)
                buffer = []
    return pd.DataFrame(datalist, columns=['名次', '股票名稱', '收盤價', '漲跌', '漲跌幅', '成交量', '1週漲跌', '1週漲跌幅']) if datalist else None

def fetch_listed_weekly_decline(url):
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        text = response.content.decode(chardet.detect(response.content)['encoding'] or 'utf-8', errors='ignore')
    except requests.RequestException as e:
        logger.error(f"❌ 無法獲取上市週跌幅網頁內容: {e}")
        return None

    soup = BeautifulSoup(text, 'html.parser')
    datalist, buffer = [], []
    flags = False
    for td in soup.find_all('td'):
        text = td.text.strip()
        try:
            int(text)
            flags = True
        except ValueError:
            pass
        if flags:
            buffer.append(text)
            if len(buffer) % 8 == 0:
                datalist.append(buffer)
                buffer = []
    return pd.DataFrame(datalist, columns=['名次', '股票名稱', '收盤價', '漲跌', '漲跌幅', '成交量', '1週漲跌', '1週漲跌幅']) if datalist else None

def fetch_otc_weekly_decline(url):
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        text = response.content.decode(chardet.detect(response.content)['encoding'] or 'utf-8', errors='ignore')
    except requests.RequestException as e:
        logger.error(f"❌ 無法獲取上櫃週跌幅網頁內容: {e}")
        return None

    soup = BeautifulSoup(text, 'html.parser')
    datalist, buffer = [], []
    flags = False
    for td in soup.find_all('td'):
        text = td.text.strip()
        try:
            int(text)
            flags = True
        except ValueError:
            pass
        if flags:
            buffer.append(text)
            if len(buffer) % 8 == 0:
                datalist.append(buffer)
                buffer = []
    return pd.DataFrame(datalist, columns=['名次', '股票名稱', '收盤價', '漲跌', '漲跌幅', '成交量', '1週漲跌', '1週漲跌幅']) if datalist else None