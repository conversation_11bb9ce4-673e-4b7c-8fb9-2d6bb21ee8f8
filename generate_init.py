import os

# 定義需要創建 __init__.py 的子目錄
directories = ['ui', 'data', 'config']

# 專案根目錄
project_root = os.path.dirname(os.path.abspath(__file__))

for directory in directories:
    # 構建子目錄路徑
    dir_path = os.path.join(project_root, directory)
    # 確保目錄存在
    os.makedirs(dir_path, exist_ok=True)
    # 構建 __init__.py 檔案路徑
    init_path = os.path.join(dir_path, '__init__.py')
    # 創建空的 __init__.py（如果不存在）
    if not os.path.exists(init_path):
        with open(init_path, 'w', encoding='utf-8') as f:
            pass  # 創建空檔案
        print(f'已創建 {init_path}')
    else:
        print(f'{init_path} 已存在')