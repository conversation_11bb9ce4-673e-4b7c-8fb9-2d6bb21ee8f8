import tkinter as tk
from tkinter import ttk
from ui.stock_info_tab import StockInfoTab
from ui.bid_tab import BidTab
from ui.info_tab import InfoTab

class StockApp:
    def __init__(self, root):
        self.root = root
        self.root.title("股市工具整合版")
        self.root.geometry("800x650")
        self.root.minsize(600, 400)
        
        # Set modern theme and configure font size
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', tabmargins=[5, 5, 5, 0], background='#f0f0f0')
        style.configure('TFrame', background='#f0f0f0')
        # Configure tab font size (e.g., 14 instead of default)
        style.configure('TNotebook.Tab', font=('微軟正黑體', 14))  # 調整字體大小為 14

        # Main container
        self.container = ttk.Frame(self.root)
        self.container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Notebook for tabs
        self.notebook = ttk.Notebook(self.container)
        self.notebook.pack(fill="both", expand=True)
        
        # Stock Info Tab
        self.stock_info_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.stock_info_tab, text=" 股票基本資料 ")

        # Bid Tab
        self.bid_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.bid_tab, text=" 競拍申購紀錄 ")
        
        # Info Tab
        self.info_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.info_tab, text=" 重大即時資訊 ")
        
        # Initialize tab UIs
        self.bid_tab_ui = BidTab(self.bid_tab, self.root)
        self.info_tab_ui = InfoTab(self.info_tab, self.root)
        self.stock_info_tab_ui = StockInfoTab(self.stock_info_tab, self.root)
                
        # Configure grid weights
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

if __name__ == "__main__":
    root = tk.Tk()
    app = StockApp(root)
    root.mainloop()