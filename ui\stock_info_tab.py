import tkinter as tk
from tkinter import ttk, messagebox
import requests
from bs4 import BeautifulSoup
import logging
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import re
import matplotlib.font_manager as fm

# Setup logging
logging.basicConfig(level=logging.CRITICAL)  # 只顯示嚴重錯誤

# Set Chinese font for Matplotlib
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # Use 'Microsoft YaHei' for Chinese support
plt.rcParams['axes.unicode_minus'] = False  # Ensure minus sign displays correctly

class StockInfoTab:
    def __init__(self, parent, root):
        self.parent = parent
        self.root = root
        
        # Configure styles to match existing app
        self.style = ttk.Style()
        self.style.configure('TLabel', font=('微軟正黑體', 12), background='#f0f0f0')
        self.style.configure('TButton', padding=6, font=('微軟正黑體', 12))
        self.style.configure('Treeview', rowheight=25, font=('微軟正黑體', 12))
        self.style.configure('Treeview.Heading', font=('微軟正黑體', 12, 'bold'))
        self.style.map('Treeview', background=[('selected', '#b3d9ff')])
        
        self.init_ui()

    def init_ui(self):
        # Input frame
        input_frame = ttk.Frame(self.parent, relief="ridge", borderwidth=1)
        input_frame.pack(fill="x", padx=10, pady=5)
        
        # 標籤、輸入框和按鈕都在同一行
        input_inner_frame = ttk.Frame(input_frame)
        input_inner_frame.pack(pady=5)
        
        # 標籤
        ttk.Label(input_inner_frame, text="輸入股票代號", font=("微軟正黑體", 14, "bold")).pack(side="left", padx=(0, 10))
        
        # 輸入框
        self.entry = ttk.Entry(input_inner_frame, width=20)
        self.entry.pack(side="left", padx=(0, 10))
        self.entry.bind('<Return>', self.show_info)
        
        # 查詢按鈕
        ttk.Button(input_inner_frame, text="🔍 查詢", command=self.show_info).pack(side="left", padx=5)
        ttk.Button(input_inner_frame, text="❌ 離開", command=self.root.quit).pack(side="left", padx=5)

        # Treeview for stock info
        self.tree = ttk.Treeview(
            self.parent,
            columns=("項目1", "資料1", "項目2", "資料2"),
            show="headings",
            selectmode="none",
            height=8
        )
        self.tree.heading("項目1", text="項目")
        self.tree.heading("資料1", text="基本資料")
        self.tree.heading("項目2", text="項目")
        self.tree.heading("資料2", text="財報資料")
        self.tree.column("項目1", width=80, anchor="center")
        self.tree.column("資料1", width=170, anchor="w")
        self.tree.column("項目2", width=80, anchor="center")
        self.tree.column("資料2", width=170, anchor="w")
        self.tree.pack(fill="both", padx=10, pady=5, expand=True)

        # Frame for revenue proportions with scrollbar
        revenue_frame = ttk.Frame(self.parent)
        revenue_frame.pack(fill="both", padx=10, pady=5, expand=True)
        
        # Treeview for revenue proportions
        self.revenue_tree = ttk.Treeview(
            revenue_frame,
            columns=("項目", "比重"),
            show="headings",
            selectmode="none",
            height=5
        )
        self.revenue_tree.heading("項目", text="項目")
        self.revenue_tree.heading("比重", text="比重")
        self.revenue_tree.column("項目", width=150, anchor="w")
        self.revenue_tree.column("比重", width=150, anchor="center")
                
        # Pack treeview and scrollbar
        self.revenue_tree.pack(side="left", fill="both", expand=True)

        # Canvas frame for pie chart
        self.canvas_frame = ttk.Frame(self.parent)
        self.canvas_frame.pack(fill="both", padx=10, pady=5, expand=True)

    def fetch_stock_info(self, stock_id):
        logging.info(f"開始抓取股票代號 {stock_id} 的資料")
        url = f"https://fubon-ebrokerdj.fbs.com.tw/z/zc/zca/zca_{stock_id}.djhtm"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        try:
            logging.debug(f"發送請求到 {url}")
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            logging.debug("請求成功")
            soup = BeautifulSoup(response.text, 'html.parser')

            # Find the table
            table = soup.find('table', class_='t01')
            if not table:
                logging.warning("未找到表格")
                return None

            # Process table rows
            rows = table.find_all('tr')
            data = {}
            target_rows = {
                1: [1],  # 公司名稱
                2: [7, 8],  # 收盤價
                4: [1, 2],  # 本益比
                6: [3, 4],  # 市值
                8: [5, 6],  # 毛利率
                9: [5, 6],  # 營利率
                10: [3, 4, 5, 6],  # 每股營收 & 淨利率
                14: [1, 2],  # 股本
                15: [1, 2],  # 成立時間
                16: [1, 2],  # 上市櫃時間
                18: [1, 2],  # 董事長
                19: [1, 2],  # 總經理
                24: [1, 2],  # 公司地址
                21: [1, 2]  # 營收比重
            }

            for i, row in enumerate(rows, 1):
                if i in target_rows:
                    cols = row.find_all('td')
                    if cols:
                        for col_idx in target_rows[i]:
                            if col_idx <= len(cols):
                                text = cols[col_idx - 1].get_text(strip=True)
                                if i == 1 and col_idx == 1:
                                    text = text.split()[0]
                                key = f"Row {i} Column {col_idx}"
                                data[key] = text

            # Calculate market value with comma formatting
            closing_price = data.get("Row 2 Column 8", "0").replace(",", "")
            capital_stock = data.get("Row 14 Column 2", "0").replace(",", "")
            try:
                closing_price = float(closing_price) if closing_price else 0.0
                capital_stock = float(capital_stock) if capital_stock else 0.0
                market_value = int(closing_price * capital_stock)
                market_value_str = "{:,}".format(market_value)
            except ValueError:
                market_value_str = "0"

            # Prepare table data
            table_data = [
                ("公司名稱", data.get("Row 1 Column 1", "-")),
                ("董事長", data.get("Row 18 Column 2", "-")),
                ("收盤價", data.get("Row 2 Column 8", "-")),
                ("總經理", data.get("Row 19 Column 2", "-")),
                ("毛利率", data.get("Row 8 Column 6", "-")),
                ("市值(億)", market_value_str),
                ("營利率", data.get("Row 9 Column 6", "-")),
                ("股本(億)", data.get("Row 14 Column 2", "-")),
                ("淨利率", data.get("Row 10 Column 6", "-")),
                ("成立時間", data.get("Row 15 Column 2", "-")),
                ("EPS", data.get("Row 10 Column 4", "-")),
                ("上市櫃時間", data.get("Row 16 Column 2", "-")),
                ("本益比", data.get("Row 4 Column 2", "-")),
                ("公司地址", data.get("Row 24 Column 2", "-")),
                ("營收比重", data.get("Row 21 Column 2", "-"))
            ]

            logging.info("資料抓取並格式化完成")
            return table_data
        except Exception as e:
            logging.error(f"抓取資料失敗: {str(e)}")
            return None

    def show_info(self, event=None):
        stock_id = self.entry.get().strip()
        if not stock_id:
            messagebox.showerror("錯誤", "請輸入股票代號!")
            logging.warning("未輸入股票代號")
            return

        # Clear existing table content
        for item in self.tree.get_children():
            self.tree.delete(item)
        for item in self.revenue_tree.get_children():
            self.revenue_tree.delete(item)

        # Fetch and display data
        table_data = self.fetch_stock_info(stock_id)
        if table_data:
            # Handle first row
            if len(table_data) > 0:
                label1, value1 = table_data[0]
                label2, value2 = ("", "")
                self.tree.insert("", tk.END, values=(label1, value1, label2, value2))

            # Display pairs, excluding revenue proportion
            for i in range(1, len(table_data) - 1, 2):
                label1, value1 = table_data[i]
                label2, value2 = table_data[i + 1] if i + 1 < len(table_data) - 1 else ("", "")
                self.tree.insert("", tk.END, values=(label1, value1, label2, value2))

            # Display revenue proportions in Treeview
            if len(table_data) > 14:
                revenue_proportion = table_data[-1][1]
                # Remove year (e.g., "(2024)") from the last entry
                proportions = revenue_proportion.split("、")
                if proportions and any(str(i) in proportions[-1] for i in range(2020, 2026)):
                    proportions[-1] = proportions[-1].split("(")[0].strip()

                # Store item names and percentages for Treeview and pie chart
                item_names = []
                percentages = []
                for prop in proportions:
                    if "%" in prop:
                        # Extract the numeric part before "%" using regex
                        match = re.search(r'([\d.]+)%', prop)
                        if match:
                            percentage = float(match.group(1))
                            # Extract item name by removing the percentage part
                            item_name = prop.replace(match.group(0), "").strip()
                            self.revenue_tree.insert("", tk.END, values=(item_name, f"{percentage:.2f}%"))
                            item_names.append(item_name)
                            percentages.append(percentage)

                # Create figure and axes with custom position
                fig = plt.Figure(figsize=(5, 4), dpi=100)
                ax = fig.add_axes([0.1, 0.1, 0.5, 0.8])  # [left, bottom, width, height], 範圍 0-1

                # Create pie chart
                wedges, texts, autotexts = ax.pie(percentages, labels=None, autopct='%1.1f%%', startangle=90)
                ax.axis('equal')

                # Add legend outside the pie
                ax.legend(wedges, item_names,
                        title="項目",
                        loc="center left",
                        bbox_to_anchor=(1.2, 0.1, 0.5, 0.8),  # 調整圖例位置，留空間給圓餅圖
                        fontsize=10)

                # Clear previous canvas if exists
                for widget in self.canvas_frame.winfo_children():
                    widget.destroy()

                # Add new canvas
                canvas = FigureCanvasTkAgg(fig, master=self.canvas_frame)
                canvas.draw()
                canvas.get_tk_widget().pack(fill="both", expand=True)
        else:
            messagebox.showerror("錯誤", "無法抓取資料，請檢查股票代號或網絡連線!")