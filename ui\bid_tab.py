import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from data.excel import export_to_excel
from data.fetch import fetch_stock_data
import pandas as pd
import os

class BidTab:
    def __init__(self, parent, root):
        self.parent = parent
        self.root = root
        self.data = []
        self.bid_data = []
        self.selected_data = []
        self.output_dir = os.path.join(os.path.expanduser("~"), "OneDrive", "桌面", "●財務●")
        self.output_filename = "競拍申購.xlsx"
        os.makedirs(self.output_dir, exist_ok=True)

        # Initialize file path variable with default path
        self.file_path_var = tk.StringVar()
        self.file_path_var.set(os.path.join(self.output_dir, self.output_filename))
        
        # Configure styles
        self.style = ttk.Style()
        self.style.configure('Treeview', rowheight=25, font=('微軟正黑體', 12))
        self.style.configure('Treeview.Heading', font=('微軟正黑體', 12, 'bold'))
        self.style.map('Treeview', background=[('selected', '#b3d9ff')])
        
        self.init_ui()

    def init_ui(self):
        # Public subscription section
        public_frame = ttk.Frame(self.parent, relief="ridge", borderwidth=1)
        public_frame.pack(fill="both", padx=10, pady=5, expand=False)
        ttk.Label(public_frame, text="申購資訊", font=("微軟正黑體", 14, "bold")).pack(pady=5)
        
        self.tree = ttk.Treeview(
            public_frame,
            columns=("日期", "代號", "名稱", "申購日期", "承銷價", "獲利", "報酬率(%)"),
            show="headings",
            selectmode="extended",
            height=5
        )
        for col in self.tree["columns"]:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=80, anchor="center")
        self.tree.column("申購日期", width=100)
        self.tree.pack(fill="both", padx=5, pady=5)
        
        # Bid section
        bid_frame = ttk.Frame(self.parent, relief="ridge", borderwidth=1)
        bid_frame.pack(fill="both", padx=10, pady=5, expand=False)
        ttk.Label(bid_frame, text="競拍資訊", font=("微軟正黑體", 14, "bold")).pack(pady=5)
        
        self.bid_tree = ttk.Treeview(
            bid_frame,
            columns=("日期", "代號", "名稱", "投標日期", "競標張數", "最低投標價格", "市價"),
            show="headings",
            selectmode="extended",
            height=5
        )
        for col in self.bid_tree["columns"]:
            self.bid_tree.heading(col, text=col)
            self.bid_tree.column(col, width=80, anchor="center")
        self.bid_tree.column("投標日期", width=100)
        self.bid_tree.column("最低投標價格", width=90)
        self.bid_tree.pack(fill="both", padx=5, pady=5)
        
        # Path selection
        path_frame = ttk.Frame(self.parent, relief="ridge", borderwidth=1)
        path_frame.pack(fill="x", padx=10, pady=5)
        ttk.Label(path_frame, text="儲存檔案：").pack(side="left", padx=(5, 5))
        path_inner_frame = ttk.Frame(path_frame)
        path_inner_frame.pack(fill="x", padx=10, pady=5)
        ttk.Entry(path_inner_frame, textvariable=self.file_path_var, width=50).pack(side="left", fill="x", expand=True, padx=(0, 5))
        ttk.Button(path_inner_frame, text="📁 選擇", command=self.select_file).pack(side="left", padx=5)
        
        # Button and quantity selection
        control_frame = ttk.Frame(self.parent)
        control_frame.pack(pady=10)
        ttk.Button(control_frame, text="🔄 更新", command=self.fetch_stock_data).pack(side="left", padx=5)
        ttk.Button(control_frame, text="💾 記錄", command=self.export_selected_to_excel).pack(side="left", padx=5)
        
        ttk.Label(control_frame, text="戶數:").pack(side="left", padx=(10, 5))
        self.quantity_var = tk.StringVar(value="1")
        ttk.Combobox(
            control_frame,
            textvariable=self.quantity_var,
            values=[str(i) for i in range(1, 6)],
            state="readonly",
            width=5
        ).pack(side="left", padx=(0, 10))
        ttk.Button(control_frame, text="❌ 離開", command=self.root.quit).pack(side="left", padx=5)
                
        # Bind double-click
        self.tree.bind("<Double-1>", self.cancel_selection)
        self.bid_tree.bind("<Double-1>", self.cancel_selection)
        
        # Initial data fetch
        self.root.after(100, self.fetch_stock_data)

    def select_file(self):
        file_path = filedialog.asksaveasfilename(
            initialdir=self.output_dir,
            initialfile=self.output_filename,
            defaultextension=".xlsx",
            filetypes=[("Excel檔案", "*.xlsx"), ("所有檔案", "*.*")],
            title="儲存競拍申購檔案"
        )
        if file_path:
            self.file_path_var.set(file_path)
            self.output_dir = os.path.dirname(file_path)
            self.output_filename = os.path.basename(file_path)

    def cancel_selection(self, event):
        treeview = self.tree if event.widget == self.tree else self.bid_tree
        item = treeview.identify('item', event.x, event.y)
        if item and item in treeview.selection():
            treeview.selection_remove(item)

    def fetch_stock_data(self):
        try:
            self.data.clear()
            self.bid_data.clear()
            for item in self.tree.get_children():
                self.tree.delete(item)
            for item in self.bid_tree.get_children():
                self.bid_tree.delete(item)
            
            public_data, bid_data, error = fetch_stock_data()
            if error:
                messagebox.showerror("錯誤", error)
                return
            
            for item in public_data:
                self.tree.insert("", tk.END, values=(
                    item["日期"], item["代號"], item["名稱"], item["申購日期"],
                    item["承銷價"], item["獲利"], item["報酬率(%)"]
                ))
                self.data.append(item)
            
            for item in bid_data:
                self.bid_tree.insert("", tk.END, values=(
                    item["日期"], item["代號"], item["名稱"], item["投標日期"],
                    item["競標張數"], item["最低投標價格"], item["市價"]
                ))
                self.bid_data.append(item)
            
        except Exception as e:
            error_msg = f"抓取資料時發生錯誤: {str(e)}\n請檢查:\n1. 網路連線\n2. 防毒軟體設定\n3. 系統時間是否正確"
            messagebox.showerror("錯誤", error_msg)

    def export_selected_to_excel(self):
        print("開始執行 export_selected_to_excel 函數")  # 調試信息
        selected_items_public = self.tree.selection()
        selected_items_bid = self.bid_tree.selection()
        print(f"選擇的申購項目: {len(selected_items_public)}, 選擇的競拍項目: {len(selected_items_bid)}")  # 調試信息

        if not (selected_items_public or selected_items_bid):
            messagebox.showwarning("警告", "請先選擇至少一筆資料")
            return
        
        self.selected_data.clear()
        quantity = int(self.quantity_var.get())
        
        for item in reversed(selected_items_public):
            item_data = self.tree.item(item)['values']
            date_obj = pd.to_datetime(item_data[0], format='%Y/%m/%d') - pd.Timedelta(days=1)
            self.selected_data.append({
                "日期前一天": date_obj.strftime('%Y年%#m月%#d日'),
                "類型": "申購",
                "股票代號": item_data[1],
                "股票名稱": item_data[2],
                "數量": quantity,
                "費用": 20 * quantity,
                "操作日期": item_data[3]
            })
        
        for item in reversed(selected_items_bid):
            item_data = self.bid_tree.item(item)['values']
            date_obj = pd.to_datetime(item_data[0], format='%Y/%m/%d') - pd.Timedelta(days=1)
            self.selected_data.append({
                "日期前一天": date_obj.strftime('%Y年%#m月%#d日'),
                "類型": "競拍",
                "股票代號": item_data[1],
                "股票名稱": item_data[2],
                "數量": quantity,
                "費用": 400 * quantity,
                "操作日期": item_data[3]
            })
        
        if self.selected_data:
            output_path = self.file_path_var.get()
            print(f"輸出路徑: {output_path}")  # 調試信息
            print(f"選擇的資料數量: {len(self.selected_data)}")  # 調試信息
            try:
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                self.export_to_excel_custom_path(output_path)
                print("Excel 檔案儲存成功")  # 調試信息
                messagebox.showinfo("成功", f"資料已儲存至 {output_path}")
            except Exception as e:
                error_msg = f"匯出Excel時發生錯誤: {str(e)}"
                print(f"錯誤: {error_msg}")  # 調試信息
                messagebox.showerror("錯誤", error_msg)

    def export_to_excel_custom_path(self, output_path):
        from openpyxl import load_workbook, Workbook
        from openpyxl.styles import Alignment, Font, NamedStyle
        from datetime import datetime
        
        try:
            def parse_date(date_str):
                try:
                    date = date_str.split('-')[0].strip()
                    current_year = pd.Timestamp.now().year
                    return pd.to_datetime(f"{current_year}/{date}", format='%Y/%m/%d')
                except (ValueError, IndexError):
                    return pd.to_datetime('1900/01/01')
            
            self.selected_data.sort(key=lambda x: parse_date(x["操作日期"]), reverse=True)
            headers = ["日期", "買賣類別", "股票代號", "股票名稱", "數量", "費用"]
            
            def write_row(ws, row_idx, data):
                # 日期欄位 - 設定為日期格式
                date_cell = ws.cell(row=row_idx, column=1)
                # 將日期字串轉換為 datetime 物件
                date_str = data["日期前一天"]
                if isinstance(date_str, str):
                    # 解析 "2025年7月22日" 格式
                    import re
                    match = re.match(r'(\d{4})年(\d{1,2})月(\d{1,2})日', date_str)
                    if match:
                        year, month, day = match.groups()
                        date_cell.value = datetime(int(year), int(month), int(day))
                    else:
                        date_cell.value = date_str
                else:
                    date_cell.value = date_str
                date_cell.number_format = 'YYYY年M月D日'
                date_cell.alignment = Alignment(horizontal='center', vertical='center')

                # 其他欄位
                ws.cell(row=row_idx, column=2).value = data["類型"]
                ws.cell(row=row_idx, column=3).value = data["股票代號"]
                ws.cell(row=row_idx, column=4).value = data["股票名稱"]

                # 數量欄位 - 設定為數量格式
                quantity_cell = ws.cell(row=row_idx, column=5)
                quantity_cell.value = int(data["數量"])
                quantity_cell.number_format = '#,##0'

                # 費用欄位 - 設定為貨幣格式
                fee_cell = ws.cell(row=row_idx, column=6)
                fee_amount = 20 if data["類型"] == "申購" else 400
                fee_cell.value = fee_amount * data["數量"]
                fee_cell.number_format = '"$"#,##0'

                # 設定其他欄位的對齊方式
                for col in range(2, 7):
                    cell = ws.cell(row=row_idx, column=col)
                    cell.alignment = Alignment(horizontal='center', vertical='center')

                # 設定類型欄位的顏色
                type_cell = ws.cell(row=row_idx, column=2)
                type_cell.font = Font(color="FF0000" if data["類型"] == "申購" else "0000FF")
            
            if os.path.exists(output_path):
                wb = load_workbook(output_path)
                ws = wb['買賣紀錄'] if '買賣紀錄' in wb.sheetnames else wb.create_sheet('買賣紀錄')
                row_idx = ws.max_row + 1 if '買賣紀錄' in wb.sheetnames else 2
            else:
                wb = Workbook()
                ws = wb.active
                ws.title = '買賣紀錄'
                for col_idx, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, column=col_idx)
                    cell.value = header
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                row_idx = 2
            
            for idx, data in enumerate(self.selected_data):
                write_row(ws, row_idx + idx, data)
            
            wb.save(output_path)
            self.selected_data.clear()
            
        except Exception as e:
            raise Exception(f"匯出Excel時發生錯誤: {str(e)}")