import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import datetime
import os
from data.fetch import fetch_infomation_stocks, fetch_attention_stocks, fetch_listed_weekly_performance, fetch_otc_weekly_performance, fetch_listed_weekly_decline, fetch_otc_weekly_decline
from data.excel import save_to_excel
from config.settings import INFO_URLS

class InfoTab:
    def __init__(self, parent, root):
        self.parent = parent
        self.root = root
        self.output_dir = os.path.join(os.path.expanduser("~"), "OneDrive", "桌面", "股市資訊")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Configure styles
        self.style = ttk.Style()
        self.style.configure('TCheckbutton', padding=5, font=('微軟正黑體', 12))
        self.style.configure('TButton', padding=6, font=('微軟正黑體', 12))
        self.style.configure('TLabel', font=('微軟正黑體', 12), background='#f0f0f0')
        
        self.init_ui()

    def init_ui(self):
        # Function selection frame
        func_frame = ttk.Frame(self.parent, relief="ridge", borderwidth=1)
        func_frame.pack(fill="x", padx=10, pady=5)
        ttk.Label(func_frame, text="功能選擇", font=("微軟正黑體", 14, "bold")).pack(pady=5)
        
        self.func_vars = {
            "即時重大訊息": tk.BooleanVar(value=True),
            "注意股": tk.BooleanVar(value=True),
            "上市週漲幅": tk.BooleanVar(value=True),
            "上櫃週漲幅": tk.BooleanVar(value=True),
            "上市週跌幅": tk.BooleanVar(value=True),
            "上櫃週跌幅": tk.BooleanVar(value=True)
        }
        
        inner_func_frame = ttk.Frame(func_frame)
        inner_func_frame.pack(pady=5)
        self.func_checkboxes = {}
        for func, var in self.func_vars.items():
            cb = ttk.Checkbutton(inner_func_frame, text=func, variable=var, command=self.toggle_keywords)
            cb.pack(side="left", padx=10)
            self.func_checkboxes[func] = cb

        # MOPS keywords frame
        mops_frame = ttk.Frame(self.parent, relief="ridge", borderwidth=1)
        mops_frame.pack(fill="x", padx=10, pady=5)
        ttk.Label(mops_frame, text="即時重大訊息關鍵字", font=("微軟正黑體", 14, "bold")).pack(pady=5)
        
        self.mops_keywords = ["董事會", "召開重大訊息", "庫藏股"]
        self.mops_vars = {kw: tk.BooleanVar(value=True) for kw in self.mops_keywords}
        
        mops_inner_frame = ttk.Frame(mops_frame)
        mops_inner_frame.pack(pady=5)
        self.mops_checkboxes = {}
        for kw, var in self.mops_vars.items():
            cb = ttk.Checkbutton(mops_inner_frame, text=kw, variable=var)
            cb.pack(side="left", padx=10)
            self.mops_checkboxes[kw] = cb
        
        ttk.Label(mops_frame, text="自訂關鍵字（用「、」分隔）：").pack(pady=5)
        self.mops_custom_entry = ttk.Entry(mops_frame, width=30)
        self.mops_custom_entry.pack(pady=5)

        # Attention keywords frame
        attn_frame = ttk.Frame(self.parent, relief="ridge", borderwidth=1)
        attn_frame.pack(fill="x", padx=10, pady=5)
        ttk.Label(attn_frame, text="注意股關鍵字", font=("微軟正黑體", 14, "bold")).pack(pady=5)
        
        self.attn_keywords = ["週轉率", "平均成交量", "累積收盤價"]
        self.attn_vars = {kw: tk.BooleanVar(value=True) for kw in self.attn_keywords}
        
        attn_inner_frame = ttk.Frame(attn_frame)
        attn_inner_frame.pack(pady=5)
        self.attn_checkboxes = {}
        for kw, var in self.attn_vars.items():
            cb = ttk.Checkbutton(attn_inner_frame, text=kw, variable=var)
            cb.pack(side="left", padx=10)
            self.attn_checkboxes[kw] = cb
        
        ttk.Label(attn_frame, text="自訂關鍵字（用「、」分隔）：").pack(pady=5)
        self.attn_custom_entry = ttk.Entry(attn_frame, width=30)
        self.attn_custom_entry.pack(pady=5)

        # Path selection frame
        path_frame = ttk.Frame(self.parent, relief="ridge", borderwidth=1)
        path_frame.pack(fill="x", padx=10, pady=5)
        ttk.Label(path_frame, text="儲存檔案：").pack(side="left", padx=(5, 5))
        path_inner_frame = ttk.Frame(path_frame)
        path_inner_frame.pack(fill="x", padx=10, pady=5)
        self.path_var = tk.StringVar(value=self.output_dir)
        ttk.Entry(path_inner_frame, textvariable=self.path_var, width=50).pack(side="left", fill="x", expand=True, padx=(0, 5))
        ttk.Button(path_inner_frame, text="📁 選擇", command=self.select_directory).pack(side="right")

        # Button frame
        btn_frame = ttk.Frame(self.parent)
        btn_frame.pack(pady=10)
        ttk.Button(btn_frame, text="▶ 執行", command=self.run).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="❌ 離開", command=self.root.quit).pack(side="left", padx=5)
        
        self.toggle_keywords()

    def select_directory(self):
        dir_path = filedialog.askdirectory(initialdir=self.output_dir, title="選擇儲存資料夾")
        if dir_path:
            self.output_dir = dir_path
            self.path_var.set(dir_path)

    def toggle_keywords(self):
        mops_enabled = self.func_vars["即時重大訊息"].get()
        attn_enabled = self.func_vars["注意股"].get()
        
        state = "normal" if mops_enabled else "disabled"
        for cb in self.mops_checkboxes.values():
            cb.config(state=state)
        self.mops_custom_entry.config(state=state)
        
        state = "normal" if attn_enabled else "disabled"
        for cb in self.attn_checkboxes.values():
            cb.config(state=state)
        self.attn_custom_entry.config(state=state)

    def get_selected_keywords(self, vars_dict, custom_entry):
        selected = [kw for kw, var in vars_dict.items() if var.get()]
        custom = custom_entry.get().strip()
        if custom:
            selected.extend([kw.strip() for kw in custom.split("、") if kw.strip()])
        return selected

    def run(self):
        try:
            attn_df = mops_df = list_df = otc_df = list_decline_df = otc_decline_df = None
            mops_keywords = self.get_selected_keywords(self.mops_vars, self.mops_custom_entry) if self.func_vars["即時重大訊息"].get() else []
            attn_keywords = self.get_selected_keywords(self.attn_vars, self.attn_custom_entry) if self.func_vars["注意股"].get() else []
            
            if self.func_vars["即時重大訊息"].get():
                mops_df = fetch_infomation_stocks(INFO_URLS["mops"])
            if self.func_vars["注意股"].get():
                attn_df = fetch_attention_stocks(INFO_URLS["attention"])
            if self.func_vars["上市週漲幅"].get():
                list_df = fetch_listed_weekly_performance(INFO_URLS["listed"])
            if self.func_vars["上櫃週漲幅"].get():
                otc_df = fetch_otc_weekly_performance(INFO_URLS["otc"])
            if self.func_vars["上市週跌幅"].get():
                list_decline_df = fetch_listed_weekly_decline(INFO_URLS["listed_decline"])
            if self.func_vars["上櫃週跌幅"].get():
                otc_decline_df = fetch_otc_weekly_decline(INFO_URLS["otc_decline"])
            
            if any([attn_df is not None, mops_df is not None, list_df is not None, otc_df is not None, list_decline_df is not None, otc_decline_df is not None]):
                filename = f'股市資訊_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                full_path = os.path.join(self.output_dir, filename)
                save_to_excel(attn_df, mops_df, list_df, otc_df, list_decline_df, otc_decline_df, full_path, attn_keywords, mops_keywords)
                messagebox.showinfo("成功", f"檔案已儲存為 {full_path}")
            else:
                messagebox.showwarning("警告", "未選擇任何功能或爬取失敗！")
        except Exception as e:
            messagebox.showerror("錯誤", f"執行時發生錯誤: {str(e)}")