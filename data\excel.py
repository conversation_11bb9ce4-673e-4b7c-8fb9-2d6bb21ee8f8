import os
import pandas as pd
import datetime
import xlsxwriter
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Alignment, Font
from tkinter import messagebox
import re
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger()

def highlight_keywords(text, keywords, formats):
    if not text or not isinstance(text, str) or not keywords:
        return text
    matches = sorted([(m.start(), m.end(), kw) for kw in keywords for m in re.finditer(re.escape(kw), text)], key=lambda x: x[0])
    if not matches:
        return text
    parts = []
    last_index = 0
    for start, end, kw in matches:
        if last_index < start:
            parts.extend([formats['black'], text[last_index:start]])
        parts.extend([formats['red'], kw])
        last_index = end
    if last_index < len(text):
        parts.extend([formats['black'], text[last_index:]])
    return parts

def save_to_excel(attn_df, mops_df, list_df, otc_df, list_decline_df, otc_decline_df, filename, attn_keywords, mops_keywords):
    # filename 現在應該是完整路徑
    with xlsxwriter.Workbook(filename) as workbook:
        formats = {
            'header': workbook.add_format({'bold': True, 'bg_color': '#D9D9D9'}),
            'black': workbook.add_format({'font_color': 'black'}),
            'red': workbook.add_format({'font_color': 'red', 'bold': True}),
            'green': workbook.add_format({'font_color': 'green'})
        }

        if mops_df is not None and not mops_df.empty:
            ws = workbook.add_worksheet('即時重大訊息')
            ws.write_row(0, 0, mops_df.columns, formats['header'])
            for r, row in enumerate(mops_df.values, 1):
                for c, val in enumerate(row):
                    val = str(val)
                    formatted = highlight_keywords(val, mops_keywords, formats)
                    ws.write(r, c, val, formats['black']) if isinstance(formatted, str) else ws.write_rich_string(r, c, *formatted)
            ws.set_column(0, len(mops_df.columns) - 1, 10)
            ws.set_column(mops_df.columns.get_loc('主旨') if '主旨' in mops_df.columns else 3, mops_df.columns.get_loc('主旨') if '主旨' in mops_df.columns else 3, 150)
            ws.write(0, 5, "關鍵字: " + "、".join(mops_keywords) if mops_keywords else "無關鍵字", formats['red'])

        if attn_df is not None and not attn_df.empty:
            ws = workbook.add_worksheet('注意股')
            ws.write_row(0, 0, attn_df.columns, formats['header'])
            for r, row in enumerate(attn_df.values, 1):
                for c, val in enumerate(row):
                    val = str(val)
                    formatted = highlight_keywords(val, attn_keywords, formats)
                    ws.write(r, c, val, formats['black']) if isinstance(formatted, str) else ws.write_rich_string(r, c, *formatted)
            ws.set_column(0, 0, 15)
            ws.set_column(1, 1, 150)
            ws.write(1, 1, "關鍵字: " + "、".join(attn_keywords) if attn_keywords else "無關鍵字", formats['red'])

        if list_df is not None and not list_df.empty:
            ws = workbook.add_worksheet('上市週漲幅')
            ws.write_row(0, 0, list_df.columns, formats['header'])
            for r, row in enumerate(list_df.values, 1):
                for c, val in enumerate(row):
                    format_to_use = formats['black']
                    if c in [3, 6] and isinstance(val, str):
                        format_to_use = formats['red'] if "+\xa0" in val else formats['green'] if float(val or 0) < 0 else formats['black']
                    elif c in [4, 7] and isinstance(val, str):
                        num = float(re.search(r'[-+]?\d*\.?\d+', val).group()) if re.search(r'[-+]?\d*\.?\d+', val) else 0
                        format_to_use = formats['red'] if num > 0 else formats['green'] if num < 0 else formats['black']
                    ws.write(r, c, val, format_to_use)
            ws.set_column(0, 0, 6)
            ws.set_column(1, 1, 25)
            ws.set_column(2, 7, 11)

        if otc_df is not None and not otc_df.empty:
            ws = workbook.add_worksheet('上櫃週漲幅')
            ws.write_row(0, 0, otc_df.columns, formats['header'])
            for r, row in enumerate(otc_df.values, 1):
                for c, val in enumerate(row):
                    format_to_use = formats['black']
                    if c in [3, 6] and isinstance(val, str):
                        format_to_use = formats['red'] if "+\xa0" in val else formats['green'] if float(val or 0) < 0 else formats['black']
                    elif c in [4, 7] and isinstance(val, str):
                        num = float(re.search(r'[-+]?\d*\.?\d+', val).group()) if re.search(r'[-+]?\d*\.?\d+', val) else 0
                        format_to_use = formats['red'] if num > 0 else formats['green'] if num < 0 else formats['black']
                    ws.write(r, c, val, format_to_use)
            ws.set_column(0, 0, 6)
            ws.set_column(1, 1, 25)
            ws.set_column(2, 7, 11)

        if list_decline_df is not None and not list_decline_df.empty:
            ws = workbook.add_worksheet('上市週跌幅')
            ws.write_row(0, 0, list_decline_df.columns, formats['header'])
            for r, row in enumerate(list_decline_df.values, 1):
                for c, val in enumerate(row):
                    format_to_use = formats['black']
                    if c in [3, 6] and isinstance(val, str):
                        format_to_use = formats['red'] if "+\xa0" in val else formats['green'] if float(val or 0) < 0 else formats['black']
                    elif c in [4, 7] and isinstance(val, str):
                        num = float(re.search(r'[-+]?\d*\.?\d+', val).group()) if re.search(r'[-+]?\d*\.?\d+', val) else 0
                        format_to_use = formats['red'] if num > 0 else formats['green'] if num < 0 else formats['black']
                    ws.write(r, c, val, format_to_use)
            ws.set_column(0, 0, 6)
            ws.set_column(1, 1, 25)
            ws.set_column(2, 7, 11)

        if otc_decline_df is not None and not otc_decline_df.empty:
            ws = workbook.add_worksheet('上櫃週跌幅')
            ws.write_row(0, 0, otc_decline_df.columns, formats['header'])
            for r, row in enumerate(otc_decline_df.values, 1):
                for c, val in enumerate(row):
                    format_to_use = formats['black']
                    if c in [3, 6] and isinstance(val, str):
                        format_to_use = formats['red'] if "+\xa0" in val else formats['green'] if float(val or 0) < 0 else formats['black']
                    elif c in [4, 7] and isinstance(val, str):
                        num = float(re.search(r'[-+]?\d*\.?\d+', val).group()) if re.search(r'[-+]?\d*\.?\d+', val) else 0
                        format_to_use = formats['red'] if num > 0 else formats['green'] if num < 0 else formats['black']
                    ws.write(r, c, val, format_to_use)
            ws.set_column(0, 0, 6)
            ws.set_column(1, 1, 25)
            ws.set_column(2, 7, 11)

    #logger.info(f"✅ Excel 檔案已儲存: {filename}")

# export_to_excel 已被 bid_tab.py 中的自定義函數替代，原始函數保留以保持兼容性
def export_to_excel(selected_data, status_label):
    if not selected_data:
        messagebox.showwarning("警告", "沒有選擇任何資料")
        return

    try:
        output_dir = os.path.join(os.path.expanduser("~"), "OneDrive", "桌面", "●財務●")
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, "競拍申購.xlsx")

        def parse_date(date_str):
            try:
                date = date_str.split('-')[0].strip()
                current_year = datetime.datetime.now().year
                return pd.to_datetime(f"{current_year}/{date}", format='%Y/%m/%d')
            except (ValueError, IndexError):
                return pd.to_datetime('1900/01/01')

        selected_data.sort(key=lambda x: parse_date(x["操作日期"]), reverse=True)
        headers = ["日期", "買賣類別", "股票代號", "股票名稱", "數量", "費用"]

        def write_row(ws, row_idx, data):
            ws.cell(row=row_idx, column=1).value = data["日期前一天"]
            ws.cell(row=row_idx, column=2).value = data["類型"]
            ws.cell(row=row_idx, column=3).value = data["股票代號"]
            ws.cell(row=row_idx, column=4).value = data["股票名稱"]
            ws.cell(row=row_idx, column=5).value = data["數量"]
            fee_cell = ws.cell(row=row_idx, column=6)
            fee_cell.value = f'=IF(B{row_idx}="申購",20,400)*E{row_idx}'
            for col in range(1, 7):
                cell = ws.cell(row=row_idx, column=col)
                cell.alignment = Alignment(horizontal='center', vertical='center')
            type_cell = ws.cell(row=row_idx, column=2)
            type_cell.font = Font(color="FF0000" if data["類型"] == "申購" else "0000FF")

        if os.path.exists(output_path):
            wb = load_workbook(output_path)
            ws = wb['買賣紀錄'] if '買賣紀錄' in wb.sheetnames else wb.create_sheet('買賣紀錄')
            row_idx = ws.max_row + 1 if '買賣紀錄' in wb.sheetnames else 2
        else:
            wb = Workbook()
            ws = wb.active
            ws.title = '買賣紀錄'
            for col_idx, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col_idx)
                cell.value = header
                cell.alignment = Alignment(horizontal='center', vertical='center')
            row_idx = 2

        for idx, data in enumerate(selected_data):
            write_row(ws, row_idx + idx, data)

        wb.save(output_path)
        selected_data.clear()
        status_label.config(text="資料已成功寫入到Excel")

    except Exception as e:
        error_msg = f"匯出Excel時發生錯誤: {str(e)}"
        status_label.config(text=error_msg)
        messagebox.showerror("錯誤", error_msg)
        logger.error(error_msg)